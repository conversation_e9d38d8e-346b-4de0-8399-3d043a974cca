<Events startTimestamp="1752113184173" logVersion="1.0.0.202503121800">
  <Command __id="10" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:06:35 EDT 2025" starttimestamp="1752113184173" timestamp="10907" />
  <Command __id="11" _type="ShellCommand" date="Wed Jul 09 22:06:41 EDT 2025" starttimestamp="1752113184173" timestamp="16836" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="17" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:06:46 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="22603" type="Run" />
  <Command __id="18" _type="ShellCommand" date="Wed Jul 09 22:06:50 EDT 2025" starttimestamp="1752113184173" timestamp="26448" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="19" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58358" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="20" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58413" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:22 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="21" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58425" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):37<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:22 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>TaggedFactory test execution time (ms):37<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test "), Diff(DELETE,"TaggedFactory"), Diff(INSERT,"BridgeSceneFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="22" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58431" type="ConsoleOutput">
    <outputString><![CDATA[>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedFactory test execution time (ms):37<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶"), Diff(INSERT,"Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58473" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62267" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):3836<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):3836<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62273" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):3836"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62278" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ExceptionCommand" date="Wed Jul 09 22:07:26 EDT 2025" starttimestamp="1752113184173" timestamp="62282" type="Exception">
    <exceptionString><![CDATA[Execution exception caused by invocation exception caused by:
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="28" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62285" type="ConsoleOutput">
    <outputString><![CDATA[Execution exception caused by invocation exception caused by:
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test BridgeSceneSingletonFromFactory
¶<<"), Diff(INSERT,"Execution exception caused by invocation exception caused by:
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="29" _type="ExceptionCommand" date="Wed Jul 09 22:07:26 EDT 2025" starttimestamp="1752113184173" timestamp="62291" type="Exception">
    <exceptionString><![CDATA[	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="30" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62327" type="ConsoleOutput">
    <outputString><![CDATA[	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Execution exception caused by invocation exception caused by:
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int"), Diff(INSERT,"	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50"), Diff(EQUAL,")
¶")]]]></diff>
  </Command>
  <Command __id="31" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62411" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):15<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)"), Diff(INSERT,">>BridgeSceneSingletonFromFactory test execution time (ms):15<<
¶>>Test Result:
¶BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="32" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62437" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):108<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"BridgeScene"), Diff(INSERT,"ConsoleSceneViewFactoryMethodDefined test execution time (ms):108<<
¶>>Test Result:
¶ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶<<
¶>>ConsoleSceneView"), Diff(EQUAL,"SingletonFromFactory test execution time (ms):"), Diff(DELETE,"15"), Diff(INSERT,"3"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Bridg"), Diff(INSERT,"Consol"), Diff(EQUAL,"eScene"), Diff(INSERT,"View"), Diff(EQUAL,"SingletonFromFactory,"), Diff(INSERT,"10"), Diff(EQUAL,"0.0% complete,"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,".0,2.0,"), Diff(DELETE,"Factory method returns null object"), Diff(EQUAL,"
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(DELETE,"ConsoleSceneView"), Diff(INSERT,"Legs"), Diff(EQUAL,"FactoryMethodDefined
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="33" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62515" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):101<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneView"), Diff(INSERT,"Legs"), Diff(EQUAL,"FactoryMethodDefined test execution time (ms):10"), Diff(DELETE,"8<<
¶>>Test Result:
¶ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶<<
¶>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
¶>>Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="34" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62522" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):101"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="35" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62562" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):42<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):42"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="36" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62572" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):42"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62583" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62624" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):51<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):51"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62638" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):51"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71560" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71567" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):0<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView"), Diff(INSERT,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):0<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedLocatable"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71571" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>TaggedLocatable test execution time (ms):2<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView"), Diff(INSERT,"Steps traced since last test:
¶
¶>>TaggedLocatable"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,"<<
¶>>Test Result:
¶Tagged"), Diff(DELETE,"ConsoleSceneView,10"), Diff(INSERT,"Locatable,"), Diff(EQUAL,"0.0% complete,0.0,0.0,"), Diff(DELETE,"
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test Tagged"), Diff(INSERT,"No class in project matching name/tag:"), Diff(EQUAL,"Locatable
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71576" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Steps traced since last test:
¶
¶>>"), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"2"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(INSERT,"ConsoleSceneViewRegistersWith"), Diff(EQUAL,"Locatable"), Diff(INSERT,"s"), Diff(EQUAL,",0.0% complete,0.0,0.0,"), Diff(DELETE,"No class in project matching name/tag:Locatable"), Diff(INSERT,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="44" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71579" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Steps traced since last test:
¶
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="45" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71584" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneView
<<
>>ConsoleSceneView test execution time (ms):0<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(INSERT,"
¶<<
¶>>ConsoleSceneView"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,"), Diff(INSERT,"5"), Diff(EQUAL,"0.0,
¶Preceding test "), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"BridgeSceneSingletonFromFactory"), Diff(EQUAL," failed.
¶Please correct the problems identified by preceding test:"), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"BridgeSceneSingletonFromFactory"), Diff(EQUAL," before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="48" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:07:43 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="79232" type="Run" />
  <Command __id="50" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 22:07:47 EDT 2025" docOffset="0" starttimestamp="1752113184173" timestamp="82892" />
  <Command __id="51" _type="ShellCommand" date="Wed Jul 09 22:07:49 EDT 2025" starttimestamp="1752113184173" timestamp="85686" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="52" _type="ShellCommand" date="Wed Jul 09 22:19:47 EDT 2025" starttimestamp="1752113184173" timestamp="802870" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="53" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 22:19:51 EDT 2025" starttimestamp="1752113184173" timestamp="807794" />
  <Command __id="54" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:19:52 EDT 2025" starttimestamp="1752113184173" timestamp="808033" />
  <Command __id="55" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:19:52 EDT 2025" starttimestamp="1752113184173" timestamp="808161" />
  <Command __id="56" _type="ShellCommand" date="Wed Jul 09 22:19:55 EDT 2025" starttimestamp="1752113184173" timestamp="811478" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="62" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:20:05 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="821726" type="Run" />
  <Command __id="63" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:20:05 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="821726" type="Run" />
  <Command __id="64" _type="ShellCommand" date="Wed Jul 09 22:20:09 EDT 2025" starttimestamp="1752113184173" timestamp="825590" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="65" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833893" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<
¶>>ConsoleSceneView test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test"), Diff(INSERT,"Running suite A2Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="66" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833931" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:18 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="67" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833951" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):37<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):37"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="68" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833957" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):37"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="69" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833960" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:18 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="70" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="834002" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="71" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837835" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):3876<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):3876<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="72" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837842" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):3876"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="73" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837852" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="74" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837872" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):20<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):20"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="75" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837875" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE," test execution time (ms):20"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="76" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837877" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="77" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837903" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):27<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):27"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="78" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837912" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE," test execution time (ms):27"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="79" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837916" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶"), Diff(INSERT,"SingletonFromFactory test execution time (ms):3"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="80" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837918" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE," test execution time (ms):3"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="81" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837920" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="82" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837944" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):26<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"LegsFactoryMethodDefined test execution time (ms):26"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="83" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837948" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):26"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="84" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837949" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="85" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837976" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):27<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):27"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="86" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837986" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):27"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="87" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="838009" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):28<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):28"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="88" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="838011" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):28"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="89" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845480" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="90" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845487" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):2<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneView"), Diff(INSERT,"GetsBridgeScene"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="91" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845489" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test Tagged"), Diff(DELETE,"ConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScen"), Diff(INSERT,"Locatabl"), Diff(EQUAL,"e
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="92" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845491" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="93" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845494" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedLocatable test execution time (ms):4<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:"), Diff(INSERT,"TaggedLocatable test execution time (ms):4<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="94" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845496" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerRegisterer]
<<
>>ConsoleSceneViewGetsBridgeScene test execution time (ms):7<<
>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedLocatabl"), Diff(INSERT,"est Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerRegisterer]
¶<<
¶>>ConsoleSceneViewGetsBridgeScen"), Diff(EQUAL,"e test execution time (ms):"), Diff(DELETE,"4"), Diff(INSERT,"7"), Diff(EQUAL,"<<
¶"), Diff(INSERT,">>Steps traced since last test:
¶")]]]></diff>
  </Command>
  <Command __id="95" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845499" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Test Result:
¶"), Diff(DELETE,"TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerReg"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running th"), Diff(EQUAL,"is"), Diff(INSERT," "), Diff(EQUAL,"te"), Diff(DELETE,"rer]"), Diff(INSERT,"st"), Diff(EQUAL,"
¶<<
¶>>"), Diff(DELETE,"ConsoleSceneViewGetsBridgeScene test execution time (ms):7<<
¶>>Steps traced since last test:"), Diff(INSERT,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="96" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845501" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"GetsBridgeScene"), Diff(INSERT,"RegistersWithLocatables"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables
¶"), Diff(INSERT,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="97" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845504" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0"), Diff(INSERT,"
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="98" _type="ExceptionCommand" date="Wed Jul 09 22:20:29 EDT 2025" starttimestamp="1752113184173" timestamp="845535" type="Exception">
    <exceptionString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="99" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845536" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<"), Diff(INSERT,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="100" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845560" type="ConsoleOutput">
    <outputString><![CDATA[	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(INSERT,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="101" _type="ConsoleOutput" date="Wed Jul 09 22:20:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="855186" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	a"), Diff(INSERT,"Re-running tes"), Diff(EQUAL,"t gradingTools.comp"), Diff(DELETE,"4"), Diff(INSERT,"3"), Diff(EQUAL,"01"), Diff(DELETE,"f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationM"), Diff(INSERT,"ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may ch"), Diff(EQUAL,"an"), Diff(DELETE,"a"), Diff(EQUAL,"ge"), Diff(DELETE,"r.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(INSERT,"."), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="102" _type="ShellCommand" date="Wed Jul 09 22:21:01 EDT 2025" starttimestamp="1752113184173" timestamp="877520" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="103" _type="ShellCommand" date="Wed Jul 09 22:21:39 EDT 2025" starttimestamp="1752113184173" timestamp="915817" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="104" _type="LocalChecksRawCommand" date="Wed Jul 09 22:21:40 EDT 2025" starttimestamp="1752113184173" timestamp="915904">
    <CSVRow><![CDATA[337,Wed Jul 09 22:20:39 EDT 2025,45,0,ConsoleSceneViewGetsBridgeScene,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,188,2,false, , , ,ConsoleSceneViewGetsBridgeScene-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="105" _type="ShellCommand" date="Wed Jul 09 22:21:49 EDT 2025" starttimestamp="1752113184173" timestamp="925653" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="106" _type="CopyCommand" date="Wed Jul 09 22:21:56 EDT 2025" starttimestamp="1752113184173" timestamp="932001" />
  <Command __id="107" _type="ShellCommand" date="Wed Jul 09 22:21:58 EDT 2025" starttimestamp="1752113184173" timestamp="933934" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="108" _type="ShellCommand" date="Wed Jul 09 22:22:53 EDT 2025" starttimestamp="1752113184173" timestamp="989263" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="109" _type="ShellCommand" date="Wed Jul 09 22:22:56 EDT 2025" starttimestamp="1752113184173" timestamp="991991" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="110" _type="ShellCommand" date="Wed Jul 09 22:23:06 EDT 2025" starttimestamp="1752113184173" timestamp="1001996" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="111" _type="ShellCommand" date="Wed Jul 09 22:23:11 EDT 2025" starttimestamp="1752113184173" timestamp="1006871" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="112" _type="ShellCommand" date="Wed Jul 09 22:23:23 EDT 2025" starttimestamp="1752113184173" timestamp="1018948" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="113" _type="ShellCommand" date="Wed Jul 09 22:23:26 EDT 2025" starttimestamp="1752113184173" timestamp="1022514" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="114" _type="ShellCommand" date="Wed Jul 09 22:25:10 EDT 2025" starttimestamp="1752113184173" timestamp="1126672" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="115" _type="ShellCommand" date="Wed Jul 09 22:25:14 EDT 2025" starttimestamp="1752113184173" timestamp="1130024" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="116" _type="ShellCommand" date="Wed Jul 09 22:26:39 EDT 2025" starttimestamp="1752113184173" timestamp="1215382" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="117" _type="ShellCommand" date="Wed Jul 09 22:26:40 EDT 2025" starttimestamp="1752113184173" timestamp="1216246" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="118" _type="ConsoleOutput" date="Wed Jul 09 22:26:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1222560" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,"Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
¶")]]]></diff>
  </Command>
