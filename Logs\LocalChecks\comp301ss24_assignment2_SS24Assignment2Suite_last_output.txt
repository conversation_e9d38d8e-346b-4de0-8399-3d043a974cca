*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
>>Running test TaggedConsoleSceneView
<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Running test TaggedLocatable
<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>Running test ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
*END_OUTPUT*
