*PRE_OUTPUT*
>>Running suite A2ConsoleSceneView
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerRegisterer]
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
*END_OUTPUT*
