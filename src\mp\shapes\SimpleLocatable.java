package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
@PropertyNames({"X", "Y"})
@EditablePropertyNames({"X", "Y"})
public class SimpleLocatable implements Locatable {
    protected int x, y;
    
    public SimpleLocatable() {
        this.x = 0;
        this.y = 0;
    }
    
    public SimpleLocatable(int x, int y) {
        this.x = x;
        this.y = y;
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        this.x = x;
    }
    
    @Override
    public int getY() {
        return y;
    }
    
    @Override
    public void setY(int y) {
        this.y = y;
    }
}
